<template>
  <div class="reminder-section" :class="{ expanded: isReminderExpanded }">
    <div class="section-header">
      <span class="section-icon">⏰</span>
      <span class="section-title">提醒事项</span>
      <div class="section-actions">
        <button class="section-add-btn" title="添加" @click="handleAddReminder">
          <span class="add-icon">+</span>
        </button>
      </div>
    </div>
    <div class="section-content" :class="{ expanded: isReminderExpanded }">
      <div v-if="loadingReminders" class="loading-text">加载中...</div>
      <div v-else-if="reminders.length === 0" class="reminder-content">
        <div class="empty-reminder">快来添加提醒事项！</div>
      </div>
      <div v-else class="reminder-list">
        <!-- 默认只显示一条提醒，展开后显示全部 -->
        <div
          v-for="reminder in isReminderExpanded ? reminders : reminders.slice(0, 1)"
          :key="reminder.reminder_id"
          class="reminder-item"
          @click="handleEditReminder(reminder)"
        >
          <div class="reminder-info">
            <div class="reminder-text">{{ reminder.display_text || '提醒事项' }}</div>
          </div>
          <div class="reminder-actions">
            <button class="delete-reminder-btn" @click.stop="handleDeleteReminder(reminder)">
              <span class="delete-icon">×</span>
            </button>
          </div>
        </div>

        <!-- 底部展开/收起按钮 - 当超过一个item时常驻显示 -->
        <div v-if="reminders.length > 1" class="content-footer">
          <span class="expand-toggle-text" @click="toggleReminderExpanded">
            {{ isReminderExpanded ? '收起' : '展开更多' }}
            <span class="arrow-icon" :class="{ expanded: isReminderExpanded }">{{ isReminderExpanded ? '↑' : '↓' }}</span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import type { IReminder, IPersonDetail } from '@/apis/memory';
import { getReminders, deleteReminder } from '@/apis/memory';
import { showFailToast, showSuccessToast } from 'vant';

// Props定义
interface IProps {
  personDetail: IPersonDetail | null;
  personId: string;
  userId: string;
  isUserProfile: boolean;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  addReminder: [];
  editReminder: [reminder: IReminder];
}>();

// 响应式数据
const isReminderExpanded = ref(false);
const reminders = ref<IReminder[]>([]);
const loadingReminders = ref(false);

// 切换提醒事项展开/收起状态
const toggleReminderExpanded = () => {
  isReminderExpanded.value = !isReminderExpanded.value;
};

// 处理添加提醒
const handleAddReminder = () => {
  emit('addReminder');
};

// 处理编辑提醒
const handleEditReminder = (reminder: IReminder) => {
  emit('editReminder', reminder);
};

// 处理删除提醒
const handleDeleteReminder = async (reminder: IReminder) => {
  try {
    console.log('🗑️ [ReminderSection] 开始删除提醒:', reminder);

    const response = await deleteReminder({
      user_id: props.userId,
      reminder_id: reminder.reminder_id,
    });

    if (response && response.success) {
      // 从本地列表中移除
      reminders.value = reminders.value.filter((r) => r.reminder_id !== reminder.reminder_id);
      showSuccessToast('提醒删除成功');
      console.log('✅ [ReminderSection] 提醒删除成功');
    } else {
      showFailToast('提醒删除失败');
      console.error('❌ [ReminderSection] 提醒删除失败:', response);
    }
  } catch (error) {
    console.error('❌ [ReminderSection] 删除提醒失败:', error);
    showFailToast('提醒删除失败');
  }
};

// 加载提醒数据
const loadReminders = async () => {
  if (!props.userId || !props.isUserProfile) {
    reminders.value = [];
    return;
  }

  try {
    loadingReminders.value = true;
    console.log('🔄 [ReminderSection] 开始获取提醒数据...');

    const response = await getReminders({ user_id: props.userId });
    console.log('📡 [ReminderSection] 提醒数据响应:', response);

    if (response && response.success) {
      // 倒序排列，后添加的在上方
      reminders.value = response.reminders || [];
      console.log('✅ [ReminderSection] 提醒数据加载成功，共', reminders.value.length, '条提醒');
    } else {
      console.warn('⚠️ [ReminderSection] 提醒数据格式异常:', response);
      reminders.value = [];
    }
  } catch (error) {
    console.error('❌ [ReminderSection] 获取提醒数据失败:', error);
    reminders.value = [];
  } finally {
    loadingReminders.value = false;
  }
};

// 监听props变化，重新加载数据
watch(
  () => [props.personId, props.userId, props.isUserProfile],
  () => {
    void loadReminders();
  },
  { immediate: true },
);

// 组件挂载时加载数据
onMounted(() => {
  void loadReminders();
});

// 暴露刷新方法给父组件
defineExpose({
  loadReminders,
});
</script>

<style lang="scss" scoped>
.reminder-section {
  border: none;
  border-radius: 16px;
  padding: 22px;
  margin-top: 24px;
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
}

// 提醒事项展开/收起控制
.reminder-section .section-content {
  max-height: 260px;
  overflow: hidden;
  transition: max-height 0.3s ease;

  &.expanded {
    max-height: none;
  }
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;

  .section-icon {
    font-size: 32px;
  }

  .section-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 34px;
    font-weight: 600;
    flex: 1;
  }

  .section-actions {
    display: flex;
    align-items: center;
    gap: 14px;
  }
}

.section-add-btn {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: 2px solid #00bcd4;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: rgba(0, 188, 212, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
  }

  .add-icon {
    font-size: 20px;
    font-weight: bold;
    color: #00bcd4;
  }
}

.section-content {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.loading-text {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  text-align: center;
  padding: 10px 0;
  font-size: 32px;
}

// 提醒事项样式
.reminder-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.reminder-item {
  background: rgba(0, 188, 212, 0.05);
  border: 2px solid rgba(0, 188, 212, 0.3);
  border-radius: 16px;
  padding: 20px;
  position: relative;
  cursor: pointer;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(0, 188, 212, 0.5);
    background: rgba(0, 188, 212, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.2);
  }
}

.reminder-info {
  flex: 1;
  padding-right: 40px;

  .reminder-text {
    color: rgba(255, 255, 255, 0.9);
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 12px;
  }
}

.reminder-actions {
  position: absolute;
  top: 4px;
  right: 4px;
}

.delete-reminder-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.6);
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-size: 18px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #ff6b6b;
    transform: scale(1.1);
  }

  .delete-icon {
    font-size: 30px;
  }
}

.reminder-content {
  .empty-reminder {
    color: rgba(255, 255, 255, 0.6);
    font-size: 30px;
    font-style: italic;
    text-align: center;
    padding: 20px 0;
    background: rgba(0, 188, 212, 0.05);
    border: 1px dashed rgba(0, 188, 212, 0.3);
    border-radius: 12px;
    line-height: 1.6;
  }
}

// 底部展开/收起文字样式
.content-footer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 188, 212, 0.2);
}

.expand-toggle-text {
  color: #00bcd4;
  font-size: 28px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;

  &:hover {
    color: #00ffff;
    transform: translateY(-1px);
  }

  .arrow-icon {
    font-size: 24px;
    transition: all 0.3s ease;
  }
}
</style>
